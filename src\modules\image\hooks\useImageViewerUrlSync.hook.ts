import { useCallback, useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { urlParamsToState, updateUrlParams } from "../utils/url-params.util";

/**
 * Interface for image viewer state that can be persisted to URL
 */
export interface ImageViewerState {
  scale: number;
  x: number;
  y: number;
}

/**
 * Custom hook for bidirectional URL and image viewer state synchronization
 * Handles reading viewer state from URL on mount and updating URL when state changes
 */
export function useImageViewerUrlSync() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isInitializing = useRef(true);
  const lastUrlParams = useRef<string>("");

  /**
   * Get initial viewer state from URL parameters
   */
  const getInitialViewerState = useCallback((): Partial<ImageViewerState> => {
    const urlParams = urlParamsToState(searchParams);
    
    const initialState: Partial<ImageViewerState> = {};
    
    if (urlParams.viewerScale !== undefined) {
      initialState.scale = urlParams.viewerScale;
    }
    
    if (urlParams.viewerX !== undefined) {
      initialState.x = urlParams.viewerX;
    }
    
    if (urlParams.viewerY !== undefined) {
      initialState.y = urlParams.viewerY;
    }
    
    return initialState;
  }, [searchParams]);

  /**
   * Update URL with current viewer state
   */
  const updateViewerStateInUrl = useCallback((viewerState: Partial<ImageViewerState>) => {
    if (isInitializing.current) return;

    const filterParams = {
      viewerScale: viewerState.scale,
      viewerX: viewerState.x,
      viewerY: viewerState.y,
    };

    const newParams = updateUrlParams(searchParams, filterParams);
    const newParamsString = newParams.toString();

    // Only update URL if parameters actually changed
    if (newParamsString !== lastUrlParams.current) {
      lastUrlParams.current = newParamsString;
      router.replace(`${window.location.pathname}?${newParamsString}`, { scroll: false });
    }
  }, [router, searchParams]);

  /**
   * Mark initialization as complete
   */
  const markInitializationComplete = useCallback(() => {
    isInitializing.current = false;
    lastUrlParams.current = searchParams.toString();
  }, [searchParams]);

  return {
    getInitialViewerState,
    updateViewerStateInUrl,
    markInitializationComplete,
  };
}
